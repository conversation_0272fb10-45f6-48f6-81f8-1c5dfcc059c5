import 'package:flutter/material.dart';
import '../../controllers/custom_player_controller.dart';
import '../../themes/player_theme.dart';
import '../menus/audio_selection_menu.dart';

class AudioButton extends StatelessWidget {
  final CustomPlayerController controller;

  const AudioButton({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(PlayerTheme.audioTrackIcon),
      color: PlayerTheme.buttonColor,
      onPressed: () => _showAudioMenu(context),
    );
  }

  void _showAudioMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => AudioSelectionMenu(controller: controller),
    );
  }
}
