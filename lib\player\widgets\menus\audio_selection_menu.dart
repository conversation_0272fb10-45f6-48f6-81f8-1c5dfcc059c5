import 'package:flutter/material.dart';
import '../../controllers/custom_player_controller.dart';
import '../../models/player_track.dart';
import '../../themes/player_theme.dart';

class AudioSelectionMenu extends StatelessWidget {
  final CustomPlayerController controller;

  const AudioSelectionMenu({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final audioTracks = controller.playerTracks.audioTracks;
        if (audioTracks.length <= 1) {
          return const SizedBox.shrink();
        }

        return PopupMenuButton<AudioTrack>(
          onSelected: controller.selectAudioTrack,
          itemBuilder: (context) => audioTracks.map((track) {
            return CheckedPopupMenuItem<AudioTrack>(
              value: track,
              checked: track.isSelected,
              child: Text(track.label),
            );
          }).toList(),
          child: const Padding(
            padding: EdgeInsets.all(8.0),
            child: Icon(
              PlayerTheme.audioTrackIcon,
              color: PlayerTheme.buttonColor,
            ),
          ),
        );
      },
    );
  }
}
