import 'dart:async';
import 'package:flutter/material.dart';
import 'package:better_player/better_player.dart';
import 'package:file_picker/file_picker.dart';
import 'package:floating/floating.dart';
import '../models/player_track.dart';

/// Custom controller that manages video player state and provides additional functionality
class CustomPlayerController extends ChangeNotifier {
  BetterPlayerController? _betterPlayerController;
  Timer? _hideControlsTimer;
  Timer? _positionUpdateTimer;

  // State variables
  bool _isControlsVisible = true;
  bool _isPlaying = false;
  bool _isMuted = false;
  bool _isFullscreen = false;
  bool _isBuffering = false;
  bool _hasError = false;
  bool _isPipMode = false;
  String? _errorMessage;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  Duration _bufferedPosition = Duration.zero;
  PlayerTracks _playerTracks = const PlayerTracks();

  // PiP related
  Floating? _floating;

  // Getters
  BetterPlayerController? get betterPlayerController => _betterPlayerController;
  bool get isControlsVisible => _isControlsVisible;
  bool get isPlaying => _isPlaying;
  bool get isMuted => _isMuted;
  bool get isFullscreen => _isFullscreen;
  bool get isBuffering => _isBuffering;
  bool get hasError => _hasError;
  bool get isPipMode => _isPipMode;
  String? get errorMessage => _errorMessage;
  double get volume => _volume;
  double get playbackSpeed => _playbackSpeed;
  Duration get position => _position;
  Duration get duration => _duration;
  Duration get bufferedPosition => _bufferedPosition;
  PlayerTracks get playerTracks => _playerTracks;

  // Progress as percentage (0.0 to 1.0)
  double get progress {
    if (duration.inMilliseconds == 0) return 0.0;
    return position.inMilliseconds / duration.inMilliseconds;
  }

  double get bufferedProgress {
    if (duration.inMilliseconds == 0) return 0.0;
    return bufferedPosition.inMilliseconds / duration.inMilliseconds;
  }

  /// Initialize the controller with a BetterPlayerController
  void initialize(BetterPlayerController controller) {
    _betterPlayerController = controller;
    _setupListeners();
    _startPositionUpdates();
    initializePip();
  }

  /// Setup listeners for the BetterPlayer controller
  void _setupListeners() {
    if (_betterPlayerController == null) return;

    _betterPlayerController!.addEventsListener((event) {
      switch (event.betterPlayerEventType) {
        case BetterPlayerEventType.initialized:
          _updateDuration();
          _updateAvailableTracks();
          break;
        case BetterPlayerEventType.play:
          _isPlaying = true;
          _isBuffering = false;
          _hasError = false;
          notifyListeners();
          break;
        case BetterPlayerEventType.pause:
          _isPlaying = false;
          notifyListeners();
          break;
        case BetterPlayerEventType.bufferingStart:
          _isBuffering = true;
          notifyListeners();
          break;
        case BetterPlayerEventType.bufferingEnd:
          _isBuffering = false;
          notifyListeners();
          break;
        case BetterPlayerEventType.exception:
          _hasError = true;
          _errorMessage = event.parameters?['exception']?.toString();
          notifyListeners();
          break;
        case BetterPlayerEventType.finished:
          _isPlaying = false;
          notifyListeners();
          break;
        case BetterPlayerEventType.openFullscreen:
          _isFullscreen = true;
          // Show controls when entering fullscreen
          showControlsTemporarily();
          notifyListeners();
          break;
        case BetterPlayerEventType.hideFullscreen:
          _isFullscreen = false;
          notifyListeners();
          break;

        default:
          break;
      }
    });
  }

  /// Start periodic position updates
  void _startPositionUpdates() {
    _positionUpdateTimer?.cancel();
    _positionUpdateTimer = Timer.periodic(
      const Duration(milliseconds: 100),
      (_) => _updatePosition(),
    );
  }

  /// Update current position and buffered position
  void _updatePosition() async {
    if (_betterPlayerController?.videoPlayerController == null) {
      return;
    }

    final videoController = _betterPlayerController!.videoPlayerController!;
    final newPosition = videoController.value.position;
    final newBufferedPosition = videoController.value.buffered.isNotEmpty
        ? videoController.value.buffered.last.end
        : Duration.zero;

    if (newPosition != _position || newBufferedPosition != _bufferedPosition) {
      _position = newPosition;
      _bufferedPosition = newBufferedPosition;
      notifyListeners();
    }
  }

  /// Update duration from video controller
  void _updateDuration() {
    if (_betterPlayerController?.videoPlayerController != null) {
      final duration =
          _betterPlayerController!.videoPlayerController!.value.duration;
      if (duration != null) {
        _duration = duration;
        notifyListeners();
      }
    }
  }

  /// Play/pause toggle
  void togglePlayPause() {
    if (_betterPlayerController == null) return;

    if (_isPlaying) {
      pause();
    } else {
      play();
    }
  }

  /// Play the video
  void play() {
    _betterPlayerController?.play();
    showControlsTemporarily();
  }

  /// Pause the video
  void pause() {
    _betterPlayerController?.pause();
    showControls();
  }

  /// Toggle mute state
  void toggleMute() {
    if (_betterPlayerController == null) return;

    _isMuted = !_isMuted;
    _betterPlayerController!.setVolume(_isMuted ? 0.0 : _volume);
    notifyListeners();
    showControlsTemporarily();
  }

  /// Set volume (0.0 to 1.0)
  void setVolume(double volume) {
    _volume = volume.clamp(0.0, 1.0);
    if (!_isMuted) {
      _betterPlayerController?.setVolume(_volume);
    }
    notifyListeners();
    showControlsTemporarily();
  }

  /// Set playback speed
  void setPlaybackSpeed(double speed) {
    _playbackSpeed = speed;
    _betterPlayerController?.setSpeed(speed);
    notifyListeners();
    showControlsTemporarily();
  }

  /// Seek to specific position
  void seekTo(Duration position) {
    final clampedPosition = Duration(
      milliseconds: position.inMilliseconds.clamp(0, _duration.inMilliseconds),
    );
    _betterPlayerController?.seekTo(clampedPosition);
    showControlsTemporarily();
  }

  /// Seek forward by specified duration
  void seekForward([Duration duration = const Duration(seconds: 10)]) {
    final newPosition = _position + duration;
    seekTo(newPosition);
  }

  /// Seek backward by specified duration
  void seekBackward([Duration duration = const Duration(seconds: 10)]) {
    final newPosition = _position - duration;
    seekTo(newPosition);
  }

  /// Show controls
  void showControls() {
    _hideControlsTimer?.cancel();
    if (!_isControlsVisible) {
      _isControlsVisible = true;
      notifyListeners();
    }
  }

  /// Hide controls
  void hideControls() {
    if (_isControlsVisible) {
      _isControlsVisible = false;
      notifyListeners();
    }
  }

  /// Show controls temporarily (will auto-hide after delay)
  void showControlsTemporarily() {
    showControls();
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), hideControls);
  }

  /// Toggle controls visibility
  void toggleControls() {
    if (_isControlsVisible) {
      hideControls();
    } else {
      showControlsTemporarily();
    }
  }

  /// Enter fullscreen mode
  void enterFullscreen() {
    _isFullscreen = true;
    // Show controls when entering fullscreen
    showControlsTemporarily();
    notifyListeners();
  }

  /// Exit fullscreen mode
  void exitFullscreen() {
    _isFullscreen = false;
    notifyListeners();
  }

  /// Toggle fullscreen mode
  void toggleFullscreen() {
    if (_isFullscreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  }

  /// Initialize PiP functionality
  Future<void> initializePip() async {
    try {
      _floating = Floating();
    } catch (e) {
      debugPrint('Error initializing PiP: $e');
    }
  }

  /// Enter Picture-in-Picture mode
  Future<void> enterPipMode() async {
    if (_floating == null) {
      await initializePip();
    }

    if (_floating != null) {
      try {
        final pipAvailable = await _floating!.isPipAvailable;
        if (pipAvailable) {
          final status = await _floating!.enable(ImmediatePiP());
          _isPipMode = status == PiPStatus.enabled;
          notifyListeners();
          showControlsTemporarily();
        } else {
          debugPrint('PiP mode is not available on this device');
        }
      } catch (e) {
        debugPrint('Error entering PiP mode: $e');
      }
    }
  }

  /// Exit Picture-in-Picture mode - Note: PiP can only be disabled by user interaction
  Future<void> exitPipMode() async {
    // Note: According to the floating package documentation,
    // PiP mode can only be disabled by user interaction with the system UI
    // We can only check the status and update our internal state
    await updatePipStatus();
  }

  /// Update PiP status from the system
  Future<void> updatePipStatus() async {
    if (_floating != null) {
      try {
        final status = await _floating!.pipStatus;
        final wasInPip = _isPipMode;
        _isPipMode = status == PiPStatus.enabled;

        if (wasInPip != _isPipMode) {
          notifyListeners();
        }
      } catch (e) {
        debugPrint('Error updating PiP status: $e');
      }
    }
  }

  /// Toggle Picture-in-Picture mode
  Future<void> togglePipMode() async {
    if (_isPipMode) {
      // Can't programmatically exit PiP, user must do it via system UI
      debugPrint('PiP mode can only be exited by user interaction');
    } else {
      await enterPipMode();
    }
  }

  /// Check if PiP is available on the device
  Future<bool> isPipAvailable() async {
    if (_floating == null) {
      await initializePip();
    }

    if (_floating != null) {
      try {
        return await _floating!.isPipAvailable;
      } catch (e) {
        debugPrint('Error checking PiP availability: $e');
        return false;
      }
    }
    return false;
  }

  /// Update available tracks from BetterPlayer
  void _updateAvailableTracks() {
    if (_betterPlayerController == null) return;

    final videoTracks = <VideoQualityTrack>[];
    final audioTracks = <AudioTrack>[];
    final subtitleTracks = <SubtitleTrack>[];

    // Get video quality tracks from BetterPlayer
    final betterPlayerTracks = _betterPlayerController!.betterPlayerAsmsTracks;
    if (betterPlayerTracks.isNotEmpty) {
      // Add "Auto" option as the first choice
      videoTracks.add(const VideoQualityTrack(
        id: 'auto',
        label: 'Auto',
        resolution: 'Adaptive quality',
        isSelected: true, // Default to auto
      ));

      for (int i = 0; i < betterPlayerTracks.length; i++) {
        final track = betterPlayerTracks[i];
        final isSelected =
            track == _betterPlayerController!.betterPlayerAsmsTrack;

        // Skip invalid tracks (0x0 resolution or invalid data)
        if (track.height != null && track.height! <= 0) continue;
        if (track.width != null && track.width! <= 0) continue;

        String label;
        String? resolution;

        if (track.height != null && track.height! > 0) {
          // Standard resolution naming
          label = _getQualityLabel(track.height!);
          resolution = '${track.width}x${track.height}';
        } else if (track.bitrate != null && track.bitrate! > 0) {
          // Fallback to bitrate-based naming
          label = _getBitrateLabel(track.bitrate!);
          resolution = null;
        } else {
          // Last resort generic naming
          label = 'Quality ${i + 1}';
          resolution = null;
        }

        videoTracks.add(VideoQualityTrack(
          id: i.toString(),
          label: label,
          resolution: resolution,
          bitrate: track.bitrate,
          isSelected: false, // Auto is selected by default
        ));
      }
    }

    // Get audio tracks from BetterPlayer
    final betterPlayerAudioTracks =
        _betterPlayerController!.betterPlayerAsmsAudioTracks;
    if (betterPlayerAudioTracks != null && betterPlayerAudioTracks.isNotEmpty) {
      // Add "Auto" option as the first choice
      audioTracks.add(const AudioTrack(
        id: 'auto',
        label: 'Auto',
        language: 'Automatic selection',
        isSelected: true, // Default to auto
      ));

      for (int i = 0; i < betterPlayerAudioTracks.length; i++) {
        final track = betterPlayerAudioTracks[i];
        final isSelected =
            track == _betterPlayerController!.betterPlayerAsmsAudioTrack;

        String label = track.label ?? 'Audio ${i + 1}';

        // Improve label with language info
        if (track.language != null && track.language!.isNotEmpty) {
          final languageName = _getLanguageName(track.language!);
          label = languageName.isNotEmpty ? languageName : track.language!;
          if (track.label != null && track.label != track.language) {
            label = '$label (${track.label})';
          }
        }

        audioTracks.add(AudioTrack(
          id: i.toString(),
          label: label,
          language: track.language,
          isSelected: false, // Auto is selected by default
        ));
      }
    }

    // Get subtitle tracks from BetterPlayer
    final betterPlayerSubtitles =
        _betterPlayerController!.betterPlayerSubtitlesSourceList;

    // Always add "None" option as the first choice
    subtitleTracks.add(const SubtitleTrack(
      id: 'none',
      label: 'None',
      type: SubtitleType.none,
      isSelected: true, // Default to none
    ));

    if (betterPlayerSubtitles.isNotEmpty) {
      for (int i = 0; i < betterPlayerSubtitles.length; i++) {
        final subtitle = betterPlayerSubtitles[i];
        final isSelected =
            subtitle == _betterPlayerController!.betterPlayerSubtitlesSource;

        String label = subtitle.name ?? 'Subtitle ${i + 1}';

        // Try to extract language from subtitle name or URL
        if (subtitle.name != null) {
          final name = subtitle.name!.toLowerCase();
          if (name.contains('english') || name.contains('en')) {
            label = 'English';
          } else if (name.contains('spanish') || name.contains('es')) {
            label = 'Spanish';
          } else if (name.contains('french') || name.contains('fr')) {
            label = 'French';
          } else if (name.contains('german') || name.contains('de')) {
            label = 'German';
          }
        }

        subtitleTracks.add(SubtitleTrack(
          id: i.toString(),
          label: label,
          url: subtitle.urls?.isNotEmpty == true ? subtitle.urls!.first : null,
          type: subtitle.type == BetterPlayerSubtitlesSourceType.network
              ? SubtitleType.url
              : SubtitleType.external,
          isSelected: isSelected &&
              !subtitleTracks
                  .first.isSelected, // Don't override "None" selection
        ));
      }
    }

    _playerTracks = PlayerTracks(
      videoTracks: videoTracks,
      audioTracks: audioTracks,
      subtitleTracks: subtitleTracks,
    );

    notifyListeners();
  }

  /// Select video quality track
  void selectVideoTrack(VideoQualityTrack track) {
    if (_betterPlayerController == null) return;

    if (track.id == 'auto') {
      // Enable automatic quality selection
      // BetterPlayer handles this automatically when no specific track is set
      debugPrint('Selected auto quality');
    } else {
      final trackIndex = int.tryParse(track.id);
      if (trackIndex != null &&
          trackIndex < _betterPlayerController!.betterPlayerAsmsTracks.length) {
        final betterPlayerTrack =
            _betterPlayerController!.betterPlayerAsmsTracks[trackIndex];
        _betterPlayerController!.setTrack(betterPlayerTrack);
      }
    }

    // Update local state
    final updatedVideoTracks = _playerTracks.videoTracks
        .map((t) => t.copyWith(isSelected: t.id == track.id))
        .toList();

    _playerTracks = _playerTracks.copyWith(videoTracks: updatedVideoTracks);
    notifyListeners();
    showControlsTemporarily();
  }

  /// Select audio track
  void selectAudioTrack(AudioTrack track) {
    if (_betterPlayerController == null) return;

    if (track.id == 'auto') {
      // Enable automatic audio track selection
      // BetterPlayer handles this automatically when no specific track is set
      debugPrint('Selected auto audio track');
    } else {
      final trackIndex = int.tryParse(track.id);
      final audioTracks = _betterPlayerController!.betterPlayerAsmsAudioTracks;

      if (trackIndex != null &&
          audioTracks != null &&
          trackIndex < audioTracks.length) {
        final betterPlayerAudioTrack = audioTracks[trackIndex];
        _betterPlayerController!.setAudioTrack(betterPlayerAudioTrack);
      }
    }

    // Update local state
    final updatedAudioTracks = _playerTracks.audioTracks
        .map((t) => t.copyWith(isSelected: t.id == track.id))
        .toList();

    _playerTracks = _playerTracks.copyWith(audioTracks: updatedAudioTracks);
    notifyListeners();
    showControlsTemporarily();
  }

  /// Select subtitle track
  void selectSubtitleTrack(SubtitleTrack track) {
    if (_betterPlayerController == null) return;

    if (track.id == 'none') {
      // Disable subtitles
      _betterPlayerController!.setupSubtitleSource(
        BetterPlayerSubtitlesSource(
          type: BetterPlayerSubtitlesSourceType.none,
        ),
      );
    } else {
      final trackIndex = int.tryParse(track.id);
      final subtitleSources =
          _betterPlayerController!.betterPlayerSubtitlesSourceList;

      if (trackIndex != null && trackIndex < subtitleSources.length) {
        final subtitleSource = subtitleSources[trackIndex];
        _betterPlayerController!.setupSubtitleSource(subtitleSource);
      }
    }

    // Update local state
    final updatedSubtitleTracks = _playerTracks.subtitleTracks
        .map((t) => t.copyWith(isSelected: t.id == track.id))
        .toList();

    _playerTracks =
        _playerTracks.copyWith(subtitleTracks: updatedSubtitleTracks);
    notifyListeners();
    showControlsTemporarily();
  }

  /// Load subtitle from URL
  Future<void> loadSubtitleFromUrl(String url, {String? name}) async {
    if (_betterPlayerController == null) return;

    try {
      final subtitleSource = BetterPlayerSubtitlesSource(
        type: BetterPlayerSubtitlesSourceType.network,
        name: name ?? 'External Subtitle',
        urls: [url],
      );

      await _betterPlayerController!.setupSubtitleSource(subtitleSource);

      // Add to local tracks
      final newSubtitle = SubtitleTrack(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        label: name ?? 'External Subtitle',
        url: url,
        type: SubtitleType.url,
        isSelected: true,
      );

      // Update subtitle tracks - deselect others and add new one
      final updatedSubtitleTracks = _playerTracks.subtitleTracks
          .map((t) => t.copyWith(isSelected: false))
          .toList();
      updatedSubtitleTracks.add(newSubtitle);

      _playerTracks =
          _playerTracks.copyWith(subtitleTracks: updatedSubtitleTracks);
      notifyListeners();
      showControlsTemporarily();
    } catch (e) {
      // Handle error - could emit an error event or show a message
      debugPrint('Error loading subtitle from URL: $e');
    }
  }

  /// Load subtitle from file
  Future<void> loadSubtitleFromFile() async {
    if (_betterPlayerController == null) return;

    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['srt', 'vtt', 'ass', 'ssa'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final filePath = file.path;

        if (filePath != null) {
          final subtitleSource = BetterPlayerSubtitlesSource(
            type: BetterPlayerSubtitlesSourceType.file,
            name: file.name,
            urls: [filePath],
          );

          await _betterPlayerController!.setupSubtitleSource(subtitleSource);

          // Add to local tracks
          final newSubtitle = SubtitleTrack(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            label: file.name,
            url: filePath,
            type: SubtitleType.file,
            isSelected: true,
          );

          // Update subtitle tracks - deselect others and add new one
          final updatedSubtitleTracks = _playerTracks.subtitleTracks
              .map((t) => t.copyWith(isSelected: false))
              .toList();
          updatedSubtitleTracks.add(newSubtitle);

          _playerTracks =
              _playerTracks.copyWith(subtitleTracks: updatedSubtitleTracks);
          notifyListeners();
          showControlsTemporarily();
        }
      }
    } catch (e) {
      // Handle error - could emit an error event or show a message
      debugPrint('Error loading subtitle from file: $e');
    }
  }

  /// Get quality label based on height
  String _getQualityLabel(int height) {
    if (height >= 2160) return '4K';
    if (height >= 1440) return '1440p';
    if (height >= 1080) return '1080p';
    if (height >= 720) return '720p';
    if (height >= 480) return '480p';
    if (height >= 360) return '360p';
    if (height >= 240) return '240p';
    return '${height}p';
  }

  /// Get quality label based on bitrate
  String _getBitrateLabel(int bitrate) {
    final kbps = (bitrate / 1000).round();
    if (kbps >= 8000) return 'High Quality';
    if (kbps >= 4000) return 'Medium Quality';
    if (kbps >= 1000) return 'Standard Quality';
    return 'Low Quality';
  }

  /// Get human-readable language name from language code
  String _getLanguageName(String languageCode) {
    final code = languageCode.toLowerCase();
    switch (code) {
      case 'en':
      case 'eng':
        return 'English';
      case 'es':
      case 'spa':
        return 'Spanish';
      case 'fr':
      case 'fra':
        return 'French';
      case 'de':
      case 'ger':
        return 'German';
      case 'it':
      case 'ita':
        return 'Italian';
      case 'pt':
      case 'por':
        return 'Portuguese';
      case 'ru':
      case 'rus':
        return 'Russian';
      case 'ja':
      case 'jpn':
        return 'Japanese';
      case 'ko':
      case 'kor':
        return 'Korean';
      case 'zh':
      case 'chi':
        return 'Chinese';
      case 'ar':
      case 'ara':
        return 'Arabic';
      case 'hi':
      case 'hin':
        return 'Hindi';
      default:
        return languageCode.toUpperCase();
    }
  }

  /// Format duration to readable string (MM:SS or HH:MM:SS)
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  void dispose() {
    _hideControlsTimer?.cancel();
    _positionUpdateTimer?.cancel();
    super.dispose();
  }
}
