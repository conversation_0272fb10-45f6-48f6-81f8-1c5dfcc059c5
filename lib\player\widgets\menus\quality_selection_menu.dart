import 'package:flutter/material.dart';
import '../../controllers/custom_player_controller.dart';
import '../../models/player_track.dart';

class QualitySelectionMenu extends StatelessWidget {
  final CustomPlayerController controller;

  const QualitySelectionMenu({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final videoTracks = controller.playerTracks.videoTracks;
        if (videoTracks.length <= 1) {
          return const SizedBox.shrink();
        }

        return PopupMenuButton<VideoQualityTrack>(
          onSelected: controller.selectVideoTrack,
          itemBuilder: (context) => videoTracks.map((track) {
            return CheckedPopupMenuItem<VideoQualityTrack>(
              value: track,
              checked: track.isSelected,
              child: Text(track.label),
            );
          }).toList(),
          child: const Padding(
            padding: EdgeInsets.all(8.0),
            child: Icon(
              Icons.settings,
              color: Colors.white,
            ),
          ),
        );
      },
    );
  }
}
