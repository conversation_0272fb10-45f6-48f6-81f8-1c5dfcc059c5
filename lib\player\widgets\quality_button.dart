import 'package:flutter/material.dart';
import '../controllers/custom_player_controller.dart';
import '../themes/player_theme.dart';
import 'menus/quality_selection_menu.dart';

class QualityButton extends StatelessWidget {
  final CustomPlayerController controller;

  const QualityButton({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(PlayerTheme.qualityIcon),
      color: PlayerTheme.buttonColor,
      onPressed: () => _showQualityMenu(context),
    );
  }

  void _showQualityMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => QualitySelectionMenu(controller: controller),
    );
  }
}
