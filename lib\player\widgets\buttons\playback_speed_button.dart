import 'package:flutter/material.dart';
import '../../controllers/custom_player_controller.dart';
import '../../themes/player_theme.dart';
import '../menus/playback_speed_selection_menu.dart';

class PlaybackSpeedButton extends StatelessWidget {
  final CustomPlayerController controller;

  const PlaybackSpeedButton({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.slow_motion_video),
      color: PlayerTheme.buttonColor,
      onPressed: () => _showPlaybackSpeedMenu(context),
    );
  }

  void _showPlaybackSpeedMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => PlaybackSpeedSelectionMenu(controller: controller),
    );
  }
}
