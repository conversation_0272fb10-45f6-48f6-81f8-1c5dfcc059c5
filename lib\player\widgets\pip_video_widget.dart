import 'package:flutter/material.dart';
import 'package:better_player/better_player.dart';
import '../controllers/custom_player_controller.dart';
import '../themes/player_theme.dart';

/// A minimal video player widget optimized for Picture-in-Picture mode
class PiPVideoWidget extends StatelessWidget {
  final CustomPlayerController controller;

  const PiPVideoWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: PlayerTheme.backgroundColor,
      child: Stack(
        children: [
          // Video player - full screen
          if (controller.betterPlayerController != null)
            Positioned.fill(
              child: BetterPlayer(controller: controller.betterPlayerController!),
            ),
          
          // Minimal controls overlay for PiP
          Positioned.fill(
            child: _buildPipControls(),
          ),
        ],
      ),
    );
  }

  Widget _buildPipControls() {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return GestureDetector(
          onTap: controller.togglePlayPause,
          behavior: HitTestBehavior.opaque,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Stack(
              children: [
                // Center play/pause button (only show when paused or buffering)
                if (!controller.isPlaying || controller.isBuffering)
                  Center(
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: PlayerTheme.controlBackgroundActive,
                        shape: BoxShape.circle,
                        boxShadow: PlayerTheme.buttonShadow,
                      ),
                      child: IconButton(
                        icon: Icon(
                          controller.isBuffering
                              ? Icons.hourglass_empty_rounded
                              : controller.isPlaying
                                  ? PlayerTheme.pauseIcon
                                  : PlayerTheme.playIcon,
                        ),
                        color: PlayerTheme.buttonColor,
                        iconSize: 24,
                        onPressed: controller.isBuffering ? null : controller.togglePlayPause,
                      ),
                    ),
                  ),
                
                // Loading indicator
                if (controller.isBuffering)
                  const Center(
                    child: CircularProgressIndicator(
                      color: PlayerTheme.primaryColor,
                      strokeWidth: 2,
                    ),
                  ),
                
                // Error overlay
                if (controller.hasError)
                  Container(
                    color: PlayerTheme.overlayColor,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline_rounded,
                            color: PlayerTheme.errorColor,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Error',
                            style: PlayerTheme.errorTextStyle.copyWith(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
