import 'package:flutter/material.dart';
import 'package:better_player/better_player.dart';
import '../../models/preset_item.dart';
import '../controllers/custom_player_controller.dart';
import '../themes/player_theme.dart';
import 'custom_controls.dart';
import 'pip_switcher.dart';
import 'pip_video_widget.dart';

class VideoPlayerWidget extends StatefulWidget {
  final PresetItem videoItem;

  const VideoPlayerWidget({
    super.key,
    required this.videoItem,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late BetterPlayerController _betterPlayerController;
  late CustomPlayerController _customController;

  @override
  void initState() {
    super.initState();
    _customController = CustomPlayerController();
    _setupPlayer();
  }

  void _setupPlayer() {
    final isNetwork = widget.videoItem.url.startsWith('http');
    final isHLS = widget.videoItem.url.contains('.m3u8');

    final dataSource = BetterPlayerDataSource(
      isNetwork
          ? BetterPlayerDataSourceType.network
          : BetterPlayerDataSourceType.file,
      widget.videoItem.url,
      liveStream: isHLS,
      headers: {
        if (widget.videoItem.agent != null)
          'User-Agent': widget.videoItem.agent!,
        if (widget.videoItem.referer != null)
          'Referer': widget.videoItem.referer!,
      },
    );

    _betterPlayerController = BetterPlayerController(
      BetterPlayerConfiguration(
        autoPlay: true,
        looping: false,
        aspectRatio: 16 / 9,
        // Disable default controls since we're using custom ones
        controlsConfiguration: const BetterPlayerControlsConfiguration(
          showControls: false,
        ),
        // Custom fullscreen page builder to include our controls
        routePageBuilder:
            (context, animation, secondaryAnimation, controllerProvider) {
          return Scaffold(
            backgroundColor: Colors.black,
            body: Stack(
              children: [
                // Video player
                Positioned.fill(child: controllerProvider),
                // Our custom controls overlay
                Positioned.fill(
                  child: CustomControls(
                    controller: _customController,
                    title: widget.videoItem.name,
                    showTitle: true,
                  ),
                ),
              ],
            ),
          );
        },
        // Custom error handling through our controller
        errorBuilder: (context, errorMessage) {
          return Container(
            color: PlayerTheme.backgroundColor,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline_rounded,
                    color: PlayerTheme.errorColor,
                    size: 64,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load video',
                    style: PlayerTheme.errorTextStyle,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    errorMessage ?? 'Unknown error occurred',
                    style: PlayerTheme.subtitleTextStyle,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
      betterPlayerDataSource: dataSource,
    );

    // Initialize our custom controller with the BetterPlayer controller
    _customController.initialize(_betterPlayerController);

    // Automatically enter fullscreen mode after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _betterPlayerController.enterFullScreen();
      }
    });
  }

  @override
  void dispose() {
    _customController.dispose();
    _betterPlayerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPiPSwitcher(
      controller: _customController,
      childWhenDisabled: _buildFullPlayerView(),
      childWhenEnabled: PiPVideoWidget(controller: _customController),
    );
  }

  Widget _buildFullPlayerView() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: PlayerTheme.backgroundColor,
      child: Stack(
        children: [
          // Video player - full screen
          Positioned.fill(
            child: BetterPlayer(controller: _betterPlayerController),
          ),
          // Custom controls overlay
          Positioned.fill(
            child: CustomControls(
              controller: _customController,
              title: widget.videoItem.name,
              showTitle: true,
              onFullscreenPressed: () {
                _betterPlayerController.toggleFullScreen();
              },
            ),
          ),
        ],
      ),
    );
  }
}
