/// Data models for video player tracks and settings

/// Represents a video quality/resolution track
class VideoQualityTrack {
  final String id;
  final String label;
  final String? resolution;
  final int? bitrate;
  final bool isSelected;

  const VideoQualityTrack({
    required this.id,
    required this.label,
    this.resolution,
    this.bitrate,
    this.isSelected = false,
  });

  VideoQualityTrack copyWith({
    String? id,
    String? label,
    String? resolution,
    int? bitrate,
    bool? isSelected,
  }) {
    return VideoQualityTrack(
      id: id ?? this.id,
      label: label ?? this.label,
      resolution: resolution ?? this.resolution,
      bitrate: bitrate ?? this.bitrate,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoQualityTrack && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VideoQualityTrack(id: $id, label: $label, resolution: $resolution, bitrate: $bitrate, isSelected: $isSelected)';
  }
}

/// Represents an audio track
class AudioTrack {
  final String id;
  final String label;
  final String? language;
  final String? codec;
  final int? bitrate;
  final bool isSelected;

  const AudioTrack({
    required this.id,
    required this.label,
    this.language,
    this.codec,
    this.bitrate,
    this.isSelected = false,
  });

  AudioTrack copyWith({
    String? id,
    String? label,
    String? language,
    String? codec,
    int? bitrate,
    bool? isSelected,
  }) {
    return AudioTrack(
      id: id ?? this.id,
      label: label ?? this.label,
      language: language ?? this.language,
      codec: codec ?? this.codec,
      bitrate: bitrate ?? this.bitrate,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AudioTrack && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AudioTrack(id: $id, label: $label, language: $language, codec: $codec, bitrate: $bitrate, isSelected: $isSelected)';
  }
}

/// Represents a subtitle track
class SubtitleTrack {
  final String id;
  final String label;
  final String? language;
  final String? url;
  final SubtitleType type;
  final bool isSelected;

  const SubtitleTrack({
    required this.id,
    required this.label,
    this.language,
    this.url,
    required this.type,
    this.isSelected = false,
  });

  SubtitleTrack copyWith({
    String? id,
    String? label,
    String? language,
    String? url,
    SubtitleType? type,
    bool? isSelected,
  }) {
    return SubtitleTrack(
      id: id ?? this.id,
      label: label ?? this.label,
      language: language ?? this.language,
      url: url ?? this.url,
      type: type ?? this.type,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SubtitleTrack && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SubtitleTrack(id: $id, label: $label, language: $language, url: $url, type: $type, isSelected: $isSelected)';
  }
}

/// Types of subtitle sources
enum SubtitleType {
  embedded,  // Subtitles embedded in the video stream
  external,  // External subtitle file (SRT, VTT, etc.)
  url,       // Subtitle loaded from URL
  file,      // Subtitle loaded from local file
  none,      // No subtitles
}

/// Extension to get display names for subtitle types
extension SubtitleTypeExtension on SubtitleType {
  String get displayName {
    switch (this) {
      case SubtitleType.embedded:
        return 'Embedded';
      case SubtitleType.external:
        return 'External';
      case SubtitleType.url:
        return 'From URL';
      case SubtitleType.file:
        return 'From File';
      case SubtitleType.none:
        return 'None';
    }
  }
}

/// Container for all player tracks
class PlayerTracks {
  final List<VideoQualityTrack> videoTracks;
  final List<AudioTrack> audioTracks;
  final List<SubtitleTrack> subtitleTracks;

  const PlayerTracks({
    this.videoTracks = const [],
    this.audioTracks = const [],
    this.subtitleTracks = const [],
  });

  PlayerTracks copyWith({
    List<VideoQualityTrack>? videoTracks,
    List<AudioTrack>? audioTracks,
    List<SubtitleTrack>? subtitleTracks,
  }) {
    return PlayerTracks(
      videoTracks: videoTracks ?? this.videoTracks,
      audioTracks: audioTracks ?? this.audioTracks,
      subtitleTracks: subtitleTracks ?? this.subtitleTracks,
    );
  }

  /// Get currently selected video track
  VideoQualityTrack? get selectedVideoTrack {
    try {
      return videoTracks.firstWhere((track) => track.isSelected);
    } catch (e) {
      return null;
    }
  }

  /// Get currently selected audio track
  AudioTrack? get selectedAudioTrack {
    try {
      return audioTracks.firstWhere((track) => track.isSelected);
    } catch (e) {
      return null;
    }
  }

  /// Get currently selected subtitle track
  SubtitleTrack? get selectedSubtitleTrack {
    try {
      return subtitleTracks.firstWhere((track) => track.isSelected);
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'PlayerTracks(videoTracks: ${videoTracks.length}, audioTracks: ${audioTracks.length}, subtitleTracks: ${subtitleTracks.length})';
  }
}
