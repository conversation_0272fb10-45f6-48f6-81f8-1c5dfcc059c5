import 'package:flutter/material.dart';
import '../../controllers/custom_player_controller.dart';
import '../../themes/player_theme.dart';

class PlaybackSpeedSelectionMenu extends StatelessWidget {
  final CustomPlayerController controller;

  const PlaybackSpeedSelectionMenu({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    const playbackSpeeds = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return PopupMenuButton<double>(
          onSelected: controller.setPlaybackSpeed,
          itemBuilder: (context) => playbackSpeeds.map((speed) {
            return CheckedPopupMenuItem<double>(
              value: speed,
              checked: controller.playbackSpeed == speed,
              child: Text('${speed}x'),
            );
          }).toList(),
          child: const Padding(
            padding: EdgeInsets.all(8.0),
            child: Icon(
              Icons.slow_motion_video,
              color: PlayerTheme.buttonColor,
            ),
          ),
        );
      },
    );
  }
}
