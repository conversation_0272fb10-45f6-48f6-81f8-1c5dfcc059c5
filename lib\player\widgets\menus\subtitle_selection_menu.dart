import 'package:flutter/material.dart';
import '../../controllers/custom_player_controller.dart';
import '../../models/player_track.dart';
import '../../themes/player_theme.dart';

class SubtitleSelectionMenu extends StatelessWidget {
  final CustomPlayerController controller;

  const SubtitleSelectionMenu({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final subtitleTracks = controller.playerTracks.subtitleTracks;
        if (subtitleTracks.isEmpty) {
          return const SizedBox.shrink();
        }

        return PopupMenuButton<SubtitleTrack>(
          onSelected: controller.selectSubtitleTrack,
          itemBuilder: (context) => subtitleTracks.map((track) {
            return CheckedPopupMenuItem<SubtitleTrack>(
              value: track,
              checked: track.isSelected,
              child: Text(track.label),
            );
          }).toList(),
          child: const Padding(
            padding: EdgeInsets.all(8.0),
            child: Icon(
              PlayerTheme.subtitlesIcon,
              color: PlayerTheme.buttonColor,
            ),
          ),
        );
      },
    );
  }
}
