import 'package:flutter/material.dart';
import 'package:floating/floating.dart';
import '../controllers/custom_player_controller.dart';

/// A widget that switches between different layouts based on PiP status
class CustomPiPSwitcher extends StatefulWidget {
  final CustomPlayerController controller;
  final Widget childWhenDisabled;
  final Widget childWhenEnabled;

  const CustomPiPSwitcher({
    super.key,
    required this.controller,
    required this.childWhenDisabled,
    required this.childWhenEnabled,
  });

  @override
  State<CustomPiPSwitcher> createState() => _CustomPiPSwitcherState();
}

class _CustomPiPSwitcherState extends State<CustomPiPSwitcher> {
  @override
  void initState() {
    super.initState();
    // Initialize PiP functionality
    widget.controller.initializePip();

    // Listen for PiP status changes
    _startPipStatusListener();
  }

  void _startPipStatusListener() {
    // Check PiP status periodically
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        widget.controller.updatePipStatus();
        _startPipStatusListener();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        // Use the floating package's PiPSwitcher
        return PiPSwitcher(
          childWhenDisabled: widget.childWhenDisabled,
          childWhenEnabled: widget.childWhenEnabled,
        );
      },
    );
  }
}
